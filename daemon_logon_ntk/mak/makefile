COPY=cp
DBSTRING=NEO223
#DBPW=kskybDB0955**
DBPW=neontk
PROC=proc
CC=g++
RM=rm
#CFLAGS = -g -D_DEBUG -lrt
#CFLAGS = -std=gnu++11 -g -lrt -w -DDEBUG=5
CFLAGS = -std=gnu++11 -g -lrt -Wall -DDEBUG=5

HOME=/home/<USER>/CLionProjects/neontk_230
ORG_D=${HOME}/daemon_logon_ntk
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

#EXT_LIB=/user/neontk/command_logon_ntk/obj/sms_ctrlsub++.o
EXT_LIB=${HOME}/command_logon_ntk/obj/sms_ctrlsub++.o
#EXT_INC=/user/neontk/command_logon_ntk/inc
EXT_INC=${HOME}/command_logon_ntk/inc

KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

ORACLE_VERSION  = 9
#ORACLE_INCLUDES = -I$(ORACLE_HOME)/rdbms/demo -I$(ORACLE_HOME)/rdbms/public
ORACLE_INCLUDES = -I$(PROC_INCLUDE)
ORACLE_LIBS     = -L$(ORACLE_HOME)/lib

GEN_FLAGS    =  -fno-exceptions -fno-rtti -D_REENTRANT=1
GEN_INCLUDES =
GEN_LIBS     = -lorapp -ldl -lclntsh  -lpthread

LOGON_SESSION_OBJ = $(OBJ_D)/logonSession.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o

SENDERMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/checkCallback.o\
		    $(OBJ_D)/Encrypt.o
		    
REPORTMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
#ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_INC = ${PROC_INCLUDE}

INCLUDE =   $(PRECOMPPUBLIC) -I$(INC_D) -I$(ORA_INC) -I/usr/include
#LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3)
SYS_LIB = /lib64:/lib
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -Wl,-rpath,$(SYS_LIB)

ORALIB = -lclntsh

#LIBS = -lnsl -lpthread -lksbase64 -lkssocket -lksconfig -lksthread
#LIBS = -lnsl -lpthread -lksbase64 -lkssocket -lksconfig -lksthread -lcrypto
LIBS = -l:libnsl.so.1 -lpthread -lksbase64 -lkssocket -lksconfig -lksthread -lcrypto

all: 	logonSession \
	logonDB \
	admin \
	monitorProcess \
	adminProcess \
	reportMMSProcDB \
	senderNtalkProDB 

#	senderMMSDB \
#	reportMMSDB \
#	senderMMSProcess \
#	reportMMSProcess \
#	senderAtalkCpool \
#	senderAtalkCryDB
	
logonDB: $(OBJ_D)/logonDB.o $(OBJ_D)/cust_lib_common.o $(OBJ_D)/packetUtil.o
	${CC} $(CFLAGS)  $(GEN_FLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH)  ${LINKFLAGS} $(ORACLE_LIBS) $(GEN_LIBS) -I${INC_D} -o $(BIN_D)/$@

senderMMSDB: $(OBJ_D)/senderMMSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

reportMMSDB: $(OBJ_D)/reportMMSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

logonSession: $(LOGON_SESSION_OBJ) 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

monitorProcess: $(OBJ_D)/monitorProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

adminProcess: $(OBJ_D)/adminProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

senderMMSProcess: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderMMSProcess.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

senderAtalkCpool: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderAtalkCpool.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -L/usr/local/openssl/lib -lksbase64 -lkssocket -lksconfig -lcrypto ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@
	
senderNtalkProDB: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderNtalkProDB.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@


senderAtalkCryDB: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderAtalkCryDB.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

reportMMSProcDB: $(REPORTMMSPROCESSDB_OBJ) $(OBJ_D)/reportMMSProcessDB.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

reportMMSProcess: $(REPORTMMSPROCESSDB_OBJ) $(OBJ_D)/reportMMSProcess.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@
	
admin: $(OBJ_D)/admin.o $(OBJ_D)/adminUtil.o
	${CC} $(CFLAGS)  $^ ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket  -lksconfig ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

$(OBJ_D)/logonDB.o: $(SRC_D)/logonDB.cpp
	$(RM) -rf $@
	$(CC) $(CFLAGS)   $(GEN_FLAGS) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(ORACLE_INCLUDES) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neontk/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/senderMMSProcess.o: $(SRC_D)/senderMMSProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I/usr/local/openssl/include -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/senderAtalkCpool.o: $(SRC_D)/senderAtalkCpool.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I/usr/local/openssl/include -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportMMSProcess.o: $(SRC_D)/reportMMSProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/senderNtalkProDB.o: $(SRC_D)/senderNtalkProDB.cpp
	$(RM) -rf $(OBJ_D)/senderNtalkProDB.*
	$(COPY) $(SRC_D)/senderNtalkProDB.cpp $(OBJ_D)/senderNtalkProDB.pc
	$(PROC) iname=$(OBJ_D)/senderNtalkProDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neontk/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderNtalkProDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -I/usr/local/openssl/include -c $(OBJ_D)/senderNtalkProDB.cpp

$(OBJ_D)/senderAtalkCryDB.o: $(SRC_D)/senderAtalkCryDB.cpp
	$(RM) -rf $(OBJ_D)/senderAtalkCryDB.*
	$(COPY) $(SRC_D)/senderAtalkCryDB.cpp $(OBJ_D)/senderAtalkCryDB.pc
	$(PROC) iname=$(OBJ_D)/senderAtalkCryDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neontk/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderAtalkCryDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -I/usr/local/openssl/include -c $(OBJ_D)/senderAtalkCryDB.cpp

$(OBJ_D)/reportMMSProcessDB.o: $(SRC_D)/reportMMSProcessDB.cpp
	$(RM) -rf $(OBJ_D)/reportMMSProcessDB.*
	$(COPY) $(SRC_D)/reportMMSProcessDB.cpp $(OBJ_D)/reportMMSProcessDB.pc
	$(PROC) iname=$(OBJ_D)/reportMMSProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neontk/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/reportMMSProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportMMSProcessDB.cpp

$(OBJ_D)/senderMMSDB.o: $(SRC_D)/senderMMSDB.cpp
	$(RM) -rf $(OBJ_D)/senderMMSDB.*
	$(COPY) $(SRC_D)/senderMMSDB.cpp $(OBJ_D)/senderMMSDB.pc
	$(PROC) iname=$(OBJ_D)/senderMMSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES SQLCHECK=FULL userid=neontk/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSDB.cpp

$(OBJ_D)/reportMMSDB.o: $(SRC_D)/reportMMSDB.cpp
	$(RM) -rf $(OBJ_D)/reportMMSDB.*
	$(COPY) $(SRC_D)/reportMMSDB.cpp $(OBJ_D)/reportMMSDB.pc
	$(PROC) iname=$(OBJ_D)/reportMMSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=neontk/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/reportMMSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportMMSDB.cpp

$(OBJ_D)/logonSession.o: $(SRC_D)/logonSession.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitorProcess.o: $(SRC_D)/monitorProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/adminProcess.o: $(SRC_D)/adminProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/admin.o: $(SRC_D)/admin.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/mmsPacketUtil.o: $(LIB_D)/mmsPacketUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketSend.o: $(LIB_D)/mmsPacketSend.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsFileProcess.o: $(LIB_D)/mmsFileProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsFileProcessBS.o: $(LIB_D)/mmsFileProcessBS.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketBase.o: $(LIB_D)/mmsPacketBase.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/adminUtil.o: $(LIB_D)/adminUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitor.o: $(LIB_D)/monitor.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/cust_lib_common.o: $(LIB_D)/cust_lib_common.c
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -c $^

$(OBJ_D)/logonUtil.o: $(LIB_D)/logonUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/dbUtil.o: $(LIB_D)/dbUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/packetUtil.o: $(LIB_D)/packetUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/checkCallback.o: $(LIB_D)/checkCallback.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/Encrypt.o: $(LIB_D)/Encrypt.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I/usr/include/ -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp
