#ifndef _SMS_CONFIG_H__
#define _SMS_CONFIG_H__

#include <stdio.h>
#include <sys/file.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>

//#include <sys/varargs.h>

#define   qfValue       qsValue 
#define   qfFree        qsFree
#define   qfiValue      qsiValue

// #define FetchEntry(NAME)	(qfValue(pEntry, "%s", NAME) != NULL) ? qfValue(pEntry, "%s", NAME) : ""
#define FetchEntry(NAME)	qfValue(pEntry, "%s", NAME)
#define FetchEntryInt(NAME)	qfiValue(pEntry, "%s", NAME)

typedef struct Q_Entry Q_Entry;
struct Q_Entry{
    char *name;
    char *value;
    struct Q_Entry *next;
};

class CKSConfig {
    public:
        Q_Entry *qfDecoder(char *filename);
        char *qReadFile(char *filename, int *size);
        Q_Entry *qsDecoder(char *str);
        FILE *qfopen(char *path, char *mode);
        int _flockopen(FILE *fp);
        char *qRemoveSpace(char *str);
        char *_makeword(char *str, char stop);
        char *qsValue(Q_Entry *first, char *format, ...);
        char *_EntryValue(Q_Entry *first, char *name);
        void qsFree(Q_Entry *first);
        void _EntryFree(Q_Entry *first);
        int qAwkStr(char array[][256], char *str, char delim);
        int qsiValue(Q_Entry *first, char *format, ...);
        int _EntryiValue(Q_Entry *first, char *name);
        char *strncpy2(char *pszDst, char *pszSrc, int nMax);

};

#endif 
