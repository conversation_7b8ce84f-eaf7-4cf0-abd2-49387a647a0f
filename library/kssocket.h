#ifndef _KS_SOCKET_H_
#define _KS_SOCKET_H_
/* socket header */
#include         <sys/types.h>
#include         <sys/stat.h>
#include        <sys/socket.h>
#include        <sys/un.h>
#include         <unistd.h>
#include         <stdio.h>
#include         <stdlib.h>
#include         <string.h>
#include        <arpa/inet.h>
#include         <errno.h>
#include        <fcntl.h>
#include <string>


class CKSSocket
{
    public:
	CKSSocket();
	virtual ~CKSSocket();
	int createDomain(char* path);
	int createDomainNon(char* path);
	int connectDomain(char* path);
	int accept();
	int accept2();
	int accept_in();
	int close();
	int send(char* buff,int len);
	int recv(char* buff,int len);
	int attach(int sockfd);
        int select();
        int select(int usec);
        int select(int sec,int usec);
        int rcvmsg(char* buff);
        int recvAllMsg(int sec = 3);
        int getSockfd();
        char* getErrMsg();
        char* getPeerIP();
        const char* getMsg();
        
    private:
        int m_sockfd;
	struct sockaddr_un m_cliaddr;
        struct sockaddr_in cli_inaddr; 
	int clilen;
        char szErrorMsg[512];
        std::string packet;
//        fd_set readfds;
//        int fd_max;
};

#endif 

